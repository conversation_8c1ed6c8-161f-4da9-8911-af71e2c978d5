<?php

namespace App\Policies;

use App\Models\Bus;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class BusPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->isOperator();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Bus $bus): bool
    {
        return $user->isAdmin() ||
               ($user->isOperator() && $user->operator->id === $bus->operator_id);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin() || $user->isOperator();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Bus $bus): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        if ($user->isOperator() && $user->operator->id === $bus->operator_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Bus $bus): bool
    {
        return $user->isAdmin() ||
               ($user->isOperator() && $user->operator->id === $bus->operator_id);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Bus $bus): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Bus $bus): bool
    {
        return $user->isAdmin();
    }
}
