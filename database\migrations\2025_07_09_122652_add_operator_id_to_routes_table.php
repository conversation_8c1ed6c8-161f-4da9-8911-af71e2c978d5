<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\QueryException;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            Schema::table('routes', function (Blueprint $table) {
                $table->foreignId('operator_id')->nullable()->constrained()->onDelete('cascade');
            });
        } catch (QueryException $e) {
            // Error code 1060 is for duplicate column. If we get this, we can safely ignore it.
            if ($e->errorInfo[1] !== 1060) {
                throw $e;
            }
        }

        // Set a default operator for existing routes
        if (DB::table('operators')->count() > 0) {
            $defaultOperatorId = DB::table('operators')->first()->id;
            DB::table('routes')->whereNull('operator_id')->update(['operator_id' => $defaultOperatorId]);
        }

        // Make the column non-nullable
        Schema::table('routes', function (Blueprint $table) {
            if (Schema::hasColumn('routes', 'operator_id')) {
                $table->unsignedBigInteger('operator_id')->nullable(false)->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('routes', function (Blueprint $table) {
            if (Schema::hasColumn('routes', 'operator_id')) {
                $table->dropForeign(['operator_id']);
                $table->dropColumn('operator_id');
            }
        });
    }
};
