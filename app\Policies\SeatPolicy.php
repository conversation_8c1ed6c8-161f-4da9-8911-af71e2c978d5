<?php

namespace App\Policies;

use App\Models\Seat;
use App\Models\User;

class SeatPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin() || $user->isOperator();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Seat $seat): bool
    {
        return $user->isAdmin() || 
               ($user->isOperator() && $user->operator->id === $seat->bus->operator_id);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin() || $user->isOperator();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Seat $seat): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        if ($user->isOperator() && $user->operator->id === $seat->bus->operator_id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Seat $seat): bool
    {
        return $user->isAdmin() || 
               ($user->isOperator() && $user->operator->id === $seat->bus->operator_id);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Seat $seat): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Seat $seat): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can block/unblock the seat.
     */
    public function block(User $user, Seat $seat): bool
    {
        return $user->isAdmin() || 
               ($user->isOperator() && $user->operator->id === $seat->bus->operator_id);
    }

    /**
     * Determine whether the user can configure seat properties.
     */
    public function configure(User $user, Seat $seat): bool
    {
        return $user->isAdmin() || 
               ($user->isOperator() && $user->operator->id === $seat->bus->operator_id);
    }
}
