<?php

namespace App\Http\Controllers\Operator;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Trip;
use Illuminate\Http\Request;

class BookingController extends Controller
{
    public function index()
    {
        $this->authorize('viewAny', Booking::class);

        $bookings = auth()->user()->operator->bookings()
                    ->with(['user', 'trip.bus', 'trip.route.fromCity', 'trip.route.toCity'])
                    ->latest()
                    ->paginate(10);

        return view('operator.bookings.index', compact('bookings'));
    }

    public function show(string $id)
    {
        $booking = auth()->user()->operator->bookings()
                    ->with(['user', 'trip.bus', 'trip.route.fromCity', 'trip.route.toCity', 'seats'])
                    ->findOrFail($id);
        $this->authorize('view', $booking);

        return view('operator.bookings.show', compact('booking'));
    }

    public function generateManifest(string $tripId)
    {
        $trip = auth()->user()->operator->trips()
                    ->with(['bookings.user', 'bookings.seats'])
                    ->findOrFail($tripId);
        $bookings = $trip->bookings;

        return view('operator.trips.manifest', compact('trip', 'bookings'));
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, string $id)
    {
        $booking = auth()->user()->operator->bookings()->findOrFail($id);
        $this->authorize('update', $booking);

        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed',
        ]);

        $booking->update(['status' => $request->status]);

        return back()->with('success', 'Booking status updated successfully.');
    }

    /**
     * Cancel a booking
     */
    public function cancel(string $id)
    {
        $booking = auth()->user()->operator->bookings()->findOrFail($id);
        $this->authorize('update', $booking);

        if ($booking->status === 'cancelled') {
            return back()->with('error', 'Booking is already cancelled.');
        }

        $booking->update(['status' => 'cancelled']);

        return back()->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Get bookings for a specific trip
     */
    public function tripBookings(string $tripId)
    {
        $trip = auth()->user()->operator->trips()
                    ->with(['bookings.user', 'bookings.seats'])
                    ->findOrFail($tripId);

        $bookings = $trip->bookings()->with(['user', 'seats'])->get();

        return view('operator.bookings.trip-bookings', compact('trip', 'bookings'));
    }

    /**
     * Export bookings to CSV
     */
    public function export(Request $request)
    {
        $query = auth()->user()->operator->bookings()
                    ->with(['user', 'trip.bus', 'trip.route.fromCity', 'trip.route.toCity']);

        if ($request->has('trip_id') && $request->trip_id) {
            $query->where('trip_id', $request->trip_id);
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->get();

        $filename = 'bookings_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Booking ID',
                'Passenger Name',
                'Passenger Email',
                'Trip',
                'Bus',
                'Departure',
                'Seats',
                'Total Amount',
                'Status',
                'Booking Date'
            ]);

            foreach ($bookings as $booking) {
                fputcsv($file, [
                    $booking->id,
                    $booking->user->name,
                    $booking->user->email,
                    $booking->trip->route->fromCity->name . ' to ' . $booking->trip->route->toCity->name,
                    $booking->trip->bus->bus_number,
                    $booking->trip->departure_datetime,
                    $booking->seats->pluck('seat_number')->implode(', '),
                    $booking->total_amount,
                    $booking->status,
                    $booking->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
