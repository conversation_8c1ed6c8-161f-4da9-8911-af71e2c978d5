<?php

namespace App\Http\Controllers\Operator;

use App\Http\Controllers\Controller;
use App\Models\Seat;
use App\Models\Bus;
use App\Models\Trip;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SeatController extends Controller
{
    public function index(string $tripId)
    {
        $trip = auth()->user()->operator->trips()->with('bus.seats')->findOrFail($tripId);
        $bus = $trip->bus;
        $seats = $bus->seats()->get()->keyBy('id');
        $bookedSeats = $trip->bookings()->with('seats')->get()->flatMap->seats->pluck('id')->toArray();

        return view('operator.seats.index', compact('trip', 'bus', 'seats', 'bookedSeats'));
    }

    public function block(Request $request, string $tripId, string $seatId)
    {
        $trip = auth()->user()->operator->trips()->findOrFail($tripId);
        $seat = $trip->bus->seats()->findOrFail($seatId);

        $seat->update(['is_available_for_booking' => false]);

        return back()->with('success', 'Seat blocked successfully.');
    }

    public function unblock(Request $request, string $tripId, string $seatId)
    {
        $trip = auth()->user()->operator->trips()->findOrFail($tripId);
        $seat = $trip->bus->seats()->findOrFail($seatId);

        $seat->update(['is_available_for_booking' => true]);

        return back()->with('success', 'Seat unblocked successfully.');
    }

    public function edit(string $tripId, string $seatId)
    {
        $trip = auth()->user()->operator->trips()->findOrFail($tripId);
        $seat = $trip->bus->seats()->findOrFail($seatId);

        return view('operator.seats.edit', compact('trip', 'seat'));
    }

    public function update(Request $request, string $tripId, string $seatId)
    {
        $trip = auth()->user()->operator->trips()->findOrFail($tripId);
        $bus = $trip->bus;
        $seat = $bus->seats()->findOrFail($seatId);

        $request->validate([
            'seat_number' => 'required|string|max:10',
            'position' => 'nullable|string|max:50',
        ]);

        if ($bus->seats()->where('seat_number', $request->seat_number)->where('id', '!=', $seat->id)->exists()) {
            return back()->withErrors(['seat_number' => 'Seat number already exists for this bus.']);
        }

        $seat->update([
            'seat_number' => $request->seat_number,
            'position' => $request->position,
        ]);

        return redirect()->route('operator.trips.seats.index', $trip)
                        ->with('success', 'Seat updated successfully.');
    }

    /**
     * Display seats for a specific bus
     */
    public function busSeats(string $busId)
    {
        $bus = auth()->user()->operator->buses()->with('seats')->findOrFail($busId);
        $this->authorize('view', $bus);

        $seats = $bus->seats()->orderBy('row_number')->orderBy('column_number')->get();

        return view('operator.seats.bus-seats', compact('bus', 'seats'));
    }

    /**
     * Show the form for creating a new seat
     */
    public function create(string $busId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $this->authorize('update', $bus);

        return view('operator.seats.create', compact('bus'));
    }

    /**
     * Store a newly created seat
     */
    public function store(Request $request, string $busId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $this->authorize('update', $bus);

        $validator = Validator::make($request->all(), [
            'seat_number' => 'required|string|max:10',
            'position' => 'required|string|max:50',
            'seat_type' => 'required|in:passenger,vip,driver,conductor,not_bookable',
            'row_number' => 'required|integer|min:1',
            'column_number' => 'required|integer|min:1',
            'side' => 'required|in:left,right,center',
            'is_available_for_booking' => 'boolean',
            'price_multiplier' => 'nullable|numeric|min:0.1|max:5.0',
            'properties' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $bus->seats()->create($request->all());

        return redirect()->route('operator.buses.seats', $bus->id)
                        ->with('success', 'Seat created successfully.');
    }

    /**
     * Show the form for editing a bus seat
     */
    public function editBusSeat(string $busId, string $seatId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $seat = $bus->seats()->findOrFail($seatId);
        $this->authorize('update', $seat);

        return view('operator.seats.edit-bus-seat', compact('bus', 'seat'));
    }

    /**
     * Update the specified bus seat
     */
    public function updateBusSeat(Request $request, string $busId, string $seatId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $seat = $bus->seats()->findOrFail($seatId);
        $this->authorize('update', $seat);

        $validator = Validator::make($request->all(), [
            'seat_number' => 'required|string|max:10',
            'position' => 'required|string|max:50',
            'seat_type' => 'required|in:passenger,vip,driver,conductor,not_bookable',
            'row_number' => 'required|integer|min:1',
            'column_number' => 'required|integer|min:1',
            'side' => 'required|in:left,right,center',
            'is_available_for_booking' => 'boolean',
            'price_multiplier' => 'nullable|numeric|min:0.1|max:5.0',
            'properties' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $seat->update($request->all());

        return redirect()->route('operator.buses.seats', $bus->id)
                        ->with('success', 'Seat updated successfully.');
    }

    /**
     * Remove the specified seat
     */
    public function destroy(string $busId, string $seatId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $seat = $bus->seats()->findOrFail($seatId);
        $this->authorize('delete', $seat);

        // Check if seat has any bookings
        if ($seat->bookingSeats()->exists()) {
            return back()->with('error', 'Cannot delete seat with existing bookings.');
        }

        $seat->delete();

        return redirect()->route('operator.buses.seats', $bus->id)
                        ->with('success', 'Seat deleted successfully.');
    }

    /**
     * Configure seat properties
     */
    public function configure(Request $request, string $busId, string $seatId)
    {
        $bus = auth()->user()->operator->buses()->findOrFail($busId);
        $seat = $bus->seats()->findOrFail($seatId);
        $this->authorize('configure', $seat);

        $validator = Validator::make($request->all(), [
            'properties' => 'required|array',
            'properties.is_vip' => 'boolean',
            'properties.has_charging_port' => 'boolean',
            'properties.has_reading_light' => 'boolean',
            'properties.is_window_seat' => 'boolean',
            'properties.is_aisle_seat' => 'boolean',
            'properties.extra_legroom' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $seat->update(['properties' => $request->properties]);

        return back()->with('success', 'Seat configuration updated successfully.');
    }
}
