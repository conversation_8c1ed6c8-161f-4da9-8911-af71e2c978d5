<?php

namespace App\Http\Controllers\Operator;

use App\Http\Controllers\Controller;
use App\Models\Route;
use App\Models\City;
use Illuminate\Http\Request;

class RouteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('viewAny', Route::class);

        $routes = auth()->user()->operator->routes()
                    ->with(['fromCity', 'toCity'])
                    ->latest()
                    ->paginate(10);

        return view('operator.routes.index', compact('routes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Route::class);

        $cities = City::orderBy('name')->get();
        return view('operator.routes.create', compact('cities'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Route::class);

        $request->validate([
            'from_city_id' => 'required|exists:cities,id',
            'to_city_id' => 'required|exists:cities,id|different:from_city_id',
            'estimated_km' => 'required|integer|min:1',
            'estimated_time' => 'required|date_format:H:i',
        ]);

        auth()->user()->operator->routes()->create($request->all());

        return redirect()->route('operator.routes.index')->with('success', 'Route created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $route = auth()->user()->operator->routes()
                    ->with(['fromCity', 'toCity'])
                    ->findOrFail($id);
        $this->authorize('view', $route);

        return view('operator.routes.show', compact('route'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $route = auth()->user()->operator->routes()->findOrFail($id);
        $this->authorize('update', $route);

        $cities = City::orderBy('name')->get();

        return view('operator.routes.edit', compact('route', 'cities'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $route = auth()->user()->operator->routes()->findOrFail($id);
        $this->authorize('update', $route);

        $request->validate([
            'from_city_id' => 'required|exists:cities,id',
            'to_city_id' => 'required|exists:cities,id|different:from_city_id',
            'estimated_km' => 'required|integer|min:1',
            'estimated_time' => 'required|date_format:H:i',
        ]);

        $route->update($request->all());

        return redirect()->route('operator.routes.index')->with('success', 'Route updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $route = auth()->user()->operator->routes()->findOrFail($id);
        $this->authorize('delete', $route);

        // Check if route has any trips
        if ($route->trips()->exists()) {
            return back()->with('error', 'Cannot delete route with existing trips.');
        }

        $route->delete();

        return redirect()->route('operator.routes.index')->with('success', 'Route deleted successfully.');
    }
}
